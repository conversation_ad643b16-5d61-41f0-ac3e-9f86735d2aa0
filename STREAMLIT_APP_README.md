# 🍣 Restaurant & Parking Assistant - Streamlit App

A complete Streamlit web application built with LangGraph workflow that helps users find sushi restaurants and parking information in Munich.

## 🚀 Features

- **LangGraph Workflow**: Implements a three-node graph structure (Start, Chatbot, End)
- **MCP Integration**: Uses Model Context Protocol for accessing restaurant and parking data
- **Interactive Chat Interface**: Streamlit-based UI for natural conversation
- **State Management**: Maintains conversation context and data persistence
- **Real-time Responses**: Powered by Groq's Qwen-QWQ-32B model

## 📋 Prerequisites

- Python 3.8+
- Required API keys:
  - `GROQ_API_KEY` - For the language model
  - Environment variables set in `.env` file

## 🛠️ Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables in `.env`:
```
GROQ_API_KEY=your_groq_api_key_here
```

## 🎯 Usage

1. **Start the application**:
```bash
streamlit run streamlit_app.py
```

2. **Open your browser** and navigate to `http://localhost:8502`

3. **Initialize the Assistant**:
   - Click the "🚀 Initialize Assistant" button in the sidebar
   - Wait for the MCP client and tools to load

4. **Start chatting**:
   - Ask questions about restaurants: "What sushi restaurants are available?"
   - Get detailed info: "Tell me about Sasou restaurant"
   - Find parking: "Where can I park near restaurants?"

## 🏗️ Architecture

### LangGraph Workflow

The application implements a three-node LangGraph workflow:

1. **Start Node**: 
   - Initializes conversation
   - Sets up initial state
   - Displays welcome message

2. **Chatbot Node**:
   - Processes user input
   - Calls MCP tools for data retrieval
   - Generates AI responses using Groq model
   - Manages conversation flow

3. **End Node**:
   - Handles conversation termination
   - Displays farewell message
   - Resets state for new conversations

### State Management

The `ConversationState` TypedDict manages:
- Message history
- Current user input
- Workflow step tracking
- Tool availability status
- Session data persistence

### MCP Tools

Three tools are available via the MCP client:
- `get_restaurant_names()`: Lists all available sushi restaurants
- `get_restaurant_data(restaurant_name)`: Gets detailed restaurant information
- `get_parking_data()`: Retrieves parking space information

## 🎮 User Interface

### Sidebar Controls
- **Initialize Assistant**: Sets up MCP client and tools
- **Reset Conversation**: Clears chat history and starts fresh
- **Status Indicators**: Shows assistant and tools availability
- **Information Panel**: Usage instructions and sample questions

### Main Chat Interface
- **Message Display**: Shows conversation history with user/assistant messages
- **Chat Input**: Text input for user questions
- **Debug Panel**: Optional debug information display

## 🔧 Troubleshooting

### Common Issues

1. **Assistant not initializing**:
   - Check GROQ_API_KEY in .env file
   - Ensure json_reader_tool.py is in the same directory
   - Verify all dependencies are installed

2. **Tools not working**:
   - Check data/sushi.json and data/parking.json exist
   - Verify MCP server is running correctly

3. **Chat not responding**:
   - Click "Initialize Assistant" first
   - Check for error messages in the interface
   - Try resetting the conversation

### Debug Mode

Enable debug mode by checking "🔍 Show Debug Info" to see:
- Current workflow step
- Tools availability status
- Message count
- Assistant initialization status

## 📁 File Structure

```
├── streamlit_app.py          # Main Streamlit application
├── json_reader_tool.py       # MCP server with data tools
├── data/
│   ├── sushi.json           # Restaurant data
│   └── parking.json         # Parking data
├── requirements.txt         # Python dependencies
├── .env                     # Environment variables
└── STREAMLIT_APP_README.md  # This documentation
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For issues or questions:
1. Check the troubleshooting section
2. Enable debug mode for more information
3. Review the console output in your browser's developer tools
4. Create an issue in the repository

---

**Happy chatting! 🍣🚗**
