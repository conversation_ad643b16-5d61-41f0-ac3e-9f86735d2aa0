# json_mcp_stdio.py
import json
from mcp.server.fastmcp import FastMCP

mcp = FastMCP("JSONReader")

@mcp.tool()
def get_restaurant_names() -> list[str]:
    """
    Read the sushi.json file and return all the available sushi restaurant names.
    """
    with open('data/sushi.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    titles = [item['title'] for item in data if 'title' in item]

    return titles


@mcp.tool()
def get_restaurant_data(restaurant_name: str) -> dict:
    """
    Read the sushi.json file and return all the details about the given restaurant.
    """
    with open("data/sushi.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    matches = [r for r in data if r.get("title") == restaurant_name]
    return matches[0] if matches else None

@mcp.tool()
def get_parking_data() -> dict:
    """
    Read the parking.json file and return the available parking spaces in Munich.
    People can park in these parking spaces near the given restaurant.
    """
    with open("data/parking.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    return data

if __name__ == "__main__":
    print("Starting MCP server over stdio...")
    mcp.run(transport="stdio")  
