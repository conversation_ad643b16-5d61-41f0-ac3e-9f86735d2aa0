[project]
name = "langgraphmcp"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "faiss-cpu>=1.11.0",
    "fastmcp>=2.8.1",
    "jq>=1.7",
    "langchain>=0.3.25",
    "langchain-community>=0.3.25",
    "langchain-core>=0.3.65",
    "langchain-groq>=0.3.2",
    "langchain-mcp-adapters>=0.1.7",
    "langchain-openai>=0.3.24",
    "langgraph>=0.4.8",
    "streamlit>=1.46.0",
    "tavily-python>=0.7.7",
]
