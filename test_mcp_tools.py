#!/usr/bin/env python3
"""
Test script to verify MCP tools are working correctly
"""
import asyncio
import os
from dotenv import load_dotenv
from langchain_mcp_adapters.client import MultiServerMCPClient

load_dotenv()

async def test_mcp_tools():
    """Test the MCP tools functionality"""
    print("🧪 Testing MCP Tools...")
    
    try:
        # Initialize MCP client
        client = MultiServerMCPClient({
            "json_reader": {
                "command": "python",
                "args": ["json_reader_tool.py"],
                "transport": "stdio"
            }
        })
        
        # Get available tools
        tools = await client.get_tools()
        print(f"✅ Found {len(tools)} tools:")
        for tool in tools:
            print(f"   - {tool.name}: {tool.description}")
        
        # Test each tool
        print("\n🔧 Testing tools...")
        
        # Test get_restaurant_names
        try:
            result = await client.call_tool("get_restaurant_names", {})
            print(f"✅ get_restaurant_names: Found {len(result.content[0].text)} restaurants")
        except Exception as e:
            print(f"❌ get_restaurant_names failed: {e}")
        
        # Test get_parking_data
        try:
            result = await client.call_tool("get_parking_data", {})
            print(f"✅ get_parking_data: Retrieved parking data")
        except Exception as e:
            print(f"❌ get_parking_data failed: {e}")
        
        # Test get_restaurant_data with a specific restaurant
        try:
            result = await client.call_tool("get_restaurant_data", {"restaurant_name": "Sasou"})
            print(f"✅ get_restaurant_data: Retrieved data for Sasou")
        except Exception as e:
            print(f"❌ get_restaurant_data failed: {e}")
        
        print("\n🎉 MCP Tools test completed!")
        return True
        
    except Exception as e:
        print(f"❌ MCP Tools test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_mcp_tools())
    if success:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
