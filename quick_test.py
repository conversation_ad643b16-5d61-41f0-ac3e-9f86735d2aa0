from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from langchain_groq import ChatGroq
import os
from dotenv import load_dotenv
import asyncio #asyncio is a library for asynchronous programming in Python.
load_dotenv()

async def hello_world():
    #MultiServerMCPClient is a client that can connect to multiple MCP servers.
    client=MultiServerMCPClient( 
        {
            "math":{
                "command":"python",
                "args":["json_reader_tool.py"], ## Ensure correct absolute path
                "transport":"stdio"
            }
        }
    )

    os.environ["GROQ_API_KEY"]=os.getenv("GROQ_API_KEY")

    tools=await client.get_tools()
    model=ChatGroq(model="qwen-qwq-32b")
    agent=create_react_agent(
        model,tools
    )

    math_response = await agent.ainvoke(
        {"messages": [{"role": "user", "content": "What are the available parking spaces?"}]}
    )

    print("Sushi response:", math_response['messages'][-1])

if __name__ == "__main__":
    asyncio.run(hello_world())
