import streamlit as st
import asyncio
import os
from typing import Dict, Any, List, TypedDict
from dotenv import load_dotenv
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_groq import ChatGroq
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

# Load environment variables
load_dotenv()

# Configure Streamlit page
st.set_page_config(
    page_title="Restaurant & Parking Assistant",
    page_icon="🍣",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Define the state structure for our graph
class ConversationState(TypedDict):
    messages: List[BaseMessage]
    user_input: str
    current_step: str
    tools_available: bool
    session_data: Dict[str, Any]

class RestaurantParkingAssistant:
    def __init__(self):
        self.client = None
        self.model = None
        self.tools = None
        self.graph = None
        
    def initialize_mcp_client(self):
        """Initialize the MCP client and tools"""
        try:
            import sys
            import os

            # Check if json_reader_tool.py exists
            if not os.path.exists("json_reader_tool.py"):
                raise Exception("json_reader_tool.py not found in current directory")

            # Check if data files exist
            if not os.path.exists("data/sushi.json"):
                raise Exception("data/sushi.json not found")
            if not os.path.exists("data/parking.json"):
                raise Exception("data/parking.json not found")

            # Get the current Python executable path
            python_executable = sys.executable
            st.info(f"Using Python executable: {python_executable}")

            self.client = MultiServerMCPClient({
                "json_reader": {
                    "command": python_executable,
                    "args": ["json_reader_tool.py"],
                    "transport": "stdio"
                }
            })

            # Set up Groq API key
            groq_key = os.getenv("GROQ_API_KEY")
            if not groq_key:
                raise Exception("GROQ_API_KEY not found in environment variables. Please check your .env file.")
            os.environ["GROQ_API_KEY"] = groq_key
            st.info("✅ Groq API key loaded successfully")

            # Get tools from MCP client using a more robust async handling
            st.info("🔧 Initializing MCP tools...")
            self.tools = self._get_tools_sync()
            st.info(f"✅ Loaded {len(self.tools)} MCP tools")

            # Initialize the model
            st.info("🤖 Initializing Groq model...")
            self.model = ChatGroq(model="qwen-qwq-32b", temperature=0.7)
            st.info("✅ Groq model initialized successfully")

            return True
        except Exception as e:
            error_msg = f"Failed to initialize MCP client: {str(e)}"
            st.error(error_msg)
            print(f"DEBUG: {error_msg}")  # Also print to console for debugging
            return False

    def _get_tools_sync(self):
        """Get tools from MCP client synchronously"""
        import asyncio
        import concurrent.futures
        import threading

        def run_async_in_thread():
            """Run async code in a separate thread with its own event loop"""
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.client.get_tools())
            finally:
                loop.close()

        # Run the async operation in a separate thread
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(run_async_in_thread)
            return future.result(timeout=30)
    
    def create_graph(self):
        """Create the LangGraph workflow with Start, Chatbot, and End nodes"""
        
        def start_node(state: ConversationState) -> ConversationState:
            """Initialize the conversation and set up initial state"""
            if not state.get("messages"):
                welcome_message = AIMessage(content="""
                🍣 Welcome to the Restaurant & Parking Assistant! 🚗
                
                I can help you with:
                • Finding sushi restaurants in Munich
                • Getting detailed restaurant information
                • Finding parking spaces near restaurants
                
                What would you like to know?
                """)
                state["messages"] = [welcome_message]
            
            state["current_step"] = "chatbot"
            state["tools_available"] = True
            
            if "session_data" not in state:
                state["session_data"] = {}
                
            return state
        
        def chatbot_node(state: ConversationState) -> ConversationState:
            """Handle user interactions and generate responses"""
            try:
                if state.get("user_input"):
                    # Add user message to conversation
                    user_message = HumanMessage(content=state["user_input"])
                    state["messages"] = add_messages(state["messages"], [user_message])

                    # Create agent with tools if available
                    if self.tools and self.model:
                        from langgraph.prebuilt import create_react_agent
                        agent = create_react_agent(self.model, self.tools)

                        # Get response from agent synchronously
                        response = agent.invoke({
                            "messages": state["messages"]
                        })

                        # Add AI response to messages
                        if response and "messages" in response:
                            ai_message = response["messages"][-1]
                            state["messages"] = add_messages(state["messages"], [ai_message])
                    else:
                        # Fallback response if tools are not available
                        fallback_message = AIMessage(content="I'm sorry, but I'm having trouble accessing my tools right now. Please try again later.")
                        state["messages"] = add_messages(state["messages"], [fallback_message])

                # Check if conversation should end
                last_message = state["messages"][-1] if state["messages"] else None
                if last_message and any(word in last_message.content.lower() for word in ["goodbye", "bye", "exit", "quit"]):
                    state["current_step"] = "end"
                else:
                    state["current_step"] = "chatbot"  # Continue conversation

            except Exception as e:
                error_message = AIMessage(content=f"I encountered an error: {str(e)}. Please try again.")
                state["messages"] = add_messages(state["messages"], [error_message])
                state["current_step"] = "chatbot"

            return state
        
        def end_node(state: ConversationState) -> ConversationState:
            """Conclude the conversation or handle termination logic"""
            farewell_message = AIMessage(content="""
            Thank you for using the Restaurant & Parking Assistant! 🙏
            
            Have a great meal and safe parking! 🍣🚗
            
            Feel free to start a new conversation anytime.
            """)
            state["messages"] = add_messages(state["messages"], [farewell_message])
            state["current_step"] = "end"
            return state
        
        # Create the graph
        workflow = StateGraph(ConversationState)
        
        # Add nodes
        workflow.add_node("start", start_node)
        workflow.add_node("chatbot", chatbot_node)
        workflow.add_node("end", end_node)
        
        # Define edges
        workflow.set_entry_point("start")
        workflow.add_edge("start", "chatbot")
        
        # Conditional edge from chatbot
        def should_continue(state: ConversationState) -> str:
            if state.get("current_step") == "end":
                return "end"
            return "chatbot"
        
        workflow.add_conditional_edges(
            "chatbot",
            should_continue,
            {
                "chatbot": "chatbot",
                "end": "end"
            }
        )
        
        workflow.add_edge("end", END)
        
        # Compile the graph
        self.graph = workflow.compile()
        
        return self.graph

    def process_conversation(self, state: ConversationState) -> ConversationState:
        """Process conversation through the graph synchronously"""
        if self.graph:
            return self.graph.invoke(state)
        else:
            raise Exception("Graph not initialized")

# Initialize the assistant
@st.cache_resource
def get_assistant():
    return RestaurantParkingAssistant()

def main():
    st.title("🍣 Restaurant & Parking Assistant 🚗")
    st.markdown("---")

    # Initialize session state
    if "conversation_state" not in st.session_state:
        st.session_state.conversation_state = {
            "messages": [],
            "user_input": "",
            "current_step": "start",
            "tools_available": False,
            "session_data": {}
        }

    if "assistant_initialized" not in st.session_state:
        st.session_state.assistant_initialized = False

    if "assistant" not in st.session_state:
        st.session_state.assistant = get_assistant()

    # Sidebar for controls and information
    with st.sidebar:
        st.header("🔧 Controls")

        # Initialize button
        if st.button("🚀 Initialize Assistant", type="primary"):
            with st.spinner("Initializing MCP client and tools..."):
                success = st.session_state.assistant.initialize_mcp_client()
                if success:
                    st.session_state.assistant.create_graph()
                    st.session_state.assistant_initialized = True
                    st.session_state.conversation_state["tools_available"] = True
                    st.success("✅ Assistant initialized successfully!")
                else:
                    st.error("❌ Failed to initialize assistant")

        # Reset conversation button
        if st.button("🔄 Reset Conversation"):
            st.session_state.conversation_state = {
                "messages": [],
                "user_input": "",
                "current_step": "start",
                "tools_available": st.session_state.conversation_state.get("tools_available", False),
                "session_data": {}
            }
            st.rerun()

        st.markdown("---")
        st.header("ℹ️ Information")
        st.markdown("""
        **Available Features:**
        - 🍣 Restaurant search and details
        - 🚗 Parking information
        - 📍 Location-based recommendations

        **Sample Questions:**
        - "What sushi restaurants are available?"
        - "Tell me about Sasou restaurant"
        - "Where can I park near restaurants?"
        """)

        # Status indicators
        st.markdown("---")
        st.header("📊 Status")
        if st.session_state.assistant_initialized:
            st.success("🟢 Assistant: Ready")
        else:
            st.warning("🟡 Assistant: Not initialized")

        if st.session_state.conversation_state.get("tools_available"):
            st.success("🟢 Tools: Available")
        else:
            st.warning("🟡 Tools: Not available")

    # Main chat interface
    st.header("💬 Chat Interface")

    # Display conversation messages
    if st.session_state.conversation_state["messages"]:
        for message in st.session_state.conversation_state["messages"]:
            if isinstance(message, HumanMessage):
                with st.chat_message("user"):
                    st.write(message.content)
            elif isinstance(message, AIMessage):
                with st.chat_message("assistant"):
                    st.write(message.content)

    # Chat input
    if st.session_state.assistant_initialized:
        user_input = st.chat_input("Ask me about restaurants or parking...")

        if user_input:
            # Update conversation state with user input
            st.session_state.conversation_state["user_input"] = user_input

            # Process through the graph
            if st.session_state.assistant.graph:
                try:
                    # Run the graph with current state
                    result = st.session_state.assistant.process_conversation(
                        st.session_state.conversation_state
                    )

                    # Update session state with result
                    st.session_state.conversation_state = result

                    # Clear user input for next iteration
                    st.session_state.conversation_state["user_input"] = ""

                    # Rerun to display new messages
                    st.rerun()

                except Exception as e:
                    st.error(f"Error processing your request: {str(e)}")
            else:
                st.error("Graph not initialized. Please initialize the assistant first.")
    else:
        st.info("👆 Please initialize the assistant first using the button in the sidebar.")

    # Display current graph state for debugging (optional)
    if st.checkbox("🔍 Show Debug Info"):
        st.subheader("Debug Information")
        st.json({
            "current_step": st.session_state.conversation_state.get("current_step"),
            "tools_available": st.session_state.conversation_state.get("tools_available"),
            "message_count": len(st.session_state.conversation_state.get("messages", [])),
            "assistant_initialized": st.session_state.assistant_initialized
        })

if __name__ == "__main__":
    main()
