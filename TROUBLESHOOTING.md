# 🔧 Troubleshooting Guide

## Common Issues and Solutions

### ❌ "Failed to initialize MCP client"

This error can occur for several reasons. Here's how to diagnose and fix it:

#### 1. **Check Environment Variables**
```bash
# Verify your .env file contains:
GROQ_API_KEY=your_actual_api_key_here
```

**Solution:**
- Ensure `.env` file exists in the project root
- Verify GROQ_API_KEY is set and valid
- Restart the Streamlit app after updating .env

#### 2. **Check Required Files**
The app needs these files to exist:
- `json_reader_tool.py` (MCP server)
- `data/sushi.json` (restaurant data)
- `data/parking.json` (parking data)

**Solution:**
```bash
# Check if files exist
ls -la json_reader_tool.py
ls -la data/sushi.json
ls -la data/parking.json
```

#### 3. **Python Executable Issues**
The MCP client needs to find the correct Python executable.

**Solution:**
- The app now automatically detects your Python executable
- Check the Streamlit interface for the message: "Using Python executable: ..."
- If the path looks wrong, you may need to activate the correct Python environment

#### 4. **Async/Event Loop Issues**
Streamlit and asyncio can sometimes conflict.

**Solution:**
- The app now uses a robust thread-based approach to handle async operations
- If you still see issues, try refreshing the browser page
- Click "Reset Conversation" and try initializing again

#### 5. **Network/Firewall Issues**
MCP client communication might be blocked.

**Solution:**
- Ensure no firewall is blocking Python processes
- Try running the app with administrator privileges (Windows) or sudo (Linux/Mac)

### 🔍 **Debugging Steps**

1. **Enable Debug Mode:**
   - Check "🔍 Show Debug Info" in the Streamlit interface
   - Look for detailed error messages

2. **Check Browser Console:**
   - Open browser developer tools (F12)
   - Look for JavaScript errors in the console

3. **Check Terminal Output:**
   - Look at the terminal where you ran `streamlit run streamlit_app.py`
   - Error messages will appear there

4. **Test MCP Tools Separately:**
   ```bash
   # Test the MCP server directly
   python json_reader_tool.py
   ```

### 🚀 **Quick Fixes**

1. **Restart Everything:**
   ```bash
   # Kill the Streamlit app (Ctrl+C)
   # Then restart
   streamlit run streamlit_app.py
   ```

2. **Clear Browser Cache:**
   - Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
   - Or open in incognito/private mode

3. **Reset Session State:**
   - Click "🔄 Reset Conversation" in the sidebar
   - Refresh the browser page

### 📋 **Environment Check**

Run this checklist:

- [ ] Python 3.8+ installed
- [ ] All requirements installed (`pip install -r requirements.txt`)
- [ ] `.env` file exists with GROQ_API_KEY
- [ ] `json_reader_tool.py` exists
- [ ] `data/sushi.json` and `data/parking.json` exist
- [ ] No other Python processes using the same ports
- [ ] Firewall allows Python network access

### 🆘 **Still Having Issues?**

If none of the above solutions work:

1. **Create a minimal test:**
   ```python
   # test_basic.py
   import os
   from dotenv import load_dotenv
   load_dotenv()
   
   print("GROQ_API_KEY:", "✅ Set" if os.getenv("GROQ_API_KEY") else "❌ Missing")
   print("json_reader_tool.py:", "✅ Exists" if os.path.exists("json_reader_tool.py") else "❌ Missing")
   print("data/sushi.json:", "✅ Exists" if os.path.exists("data/sushi.json") else "❌ Missing")
   print("data/parking.json:", "✅ Exists" if os.path.exists("data/parking.json") else "❌ Missing")
   ```

2. **Run the test:**
   ```bash
   python test_basic.py
   ```

3. **Check the output and fix any missing components**

### 💡 **Pro Tips**

- Always check the Streamlit interface messages during initialization
- The app provides detailed feedback about each initialization step
- Use the debug panel to see the current state
- If initialization fails, try clicking the button again after a few seconds

---

**Need more help?** Check the main README.md file or create an issue in the repository.
