#!/usr/bin/env python3
"""
Basic environment and file check for the Streamlit MCP app
"""
import os
import sys
from dotenv import load_dotenv

def main():
    print("🔍 Basic Environment Check")
    print("=" * 40)
    
    # Load environment variables
    load_dotenv()
    
    # Check Python version
    print(f"Python version: {sys.version}")
    print(f"Python executable: {sys.executable}")
    print()
    
    # Check environment variables
    print("📋 Environment Variables:")
    groq_key = os.getenv("GROQ_API_KEY")
    if groq_key:
        print(f"✅ GROQ_API_KEY: Set (length: {len(groq_key)})")
    else:
        print("❌ GROQ_API_KEY: Missing")
    print()
    
    # Check required files
    print("📁 Required Files:")
    files_to_check = [
        "json_reader_tool.py",
        "data/sushi.json", 
        "data/parking.json",
        ".env",
        "requirements.txt"
    ]
    
    all_files_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path}: Exists ({size} bytes)")
        else:
            print(f"❌ {file_path}: Missing")
            all_files_exist = False
    print()
    
    # Check data directory
    print("📂 Data Directory:")
    if os.path.exists("data"):
        data_files = os.listdir("data")
        print(f"✅ data/ directory exists with {len(data_files)} files:")
        for file in data_files:
            print(f"   - {file}")
    else:
        print("❌ data/ directory missing")
        all_files_exist = False
    print()
    
    # Check current working directory
    print("📍 Current Working Directory:")
    print(f"   {os.getcwd()}")
    print()
    
    # Summary
    print("📊 Summary:")
    if groq_key and all_files_exist:
        print("✅ All checks passed! The app should work correctly.")
    else:
        print("❌ Some issues found. Please fix the missing components.")
        print("\n🔧 Next Steps:")
        if not groq_key:
            print("   1. Create/update .env file with your GROQ_API_KEY")
        if not all_files_exist:
            print("   2. Ensure all required files are in the correct locations")
        print("   3. Run this test again to verify fixes")

if __name__ == "__main__":
    main()
